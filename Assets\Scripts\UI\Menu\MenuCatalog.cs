using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class MenuCatalog : UIMenuBase
{
    public Image bg;
    public Sprite[] panelBgs;

    public UISelectButton[] Btns;

    public List<GameObject> panels;

    public GameObject btnsParent;
    public GameObject progressParent;
    public GameObject closeButton;

    [Header("武器")]
    public UICatalogWeaponItem weaponItemPrefabe;
    public RectTransform weaponItemParent;

    [Header("随从")]
    public UICatalogWeaponItem followerItemPrefabe;
    public RectTransform followerItemParent;

    [Header("祝福")]
    public UICatalogWeaponItem blessItemPrefabe;
    public RectTransform blessItemParent;

    private int CurSelectIndex;

    //初始化
    private bool hasFollowerPanelInit;
    private bool hasBlessPanelInit;


    private void InitCatalogWeapon()//武器图鉴
    {
        ContentSizeFitter size = weaponItemParent.GetComponent<ContentSizeFitter>();
        size.enabled = true;
        CombineWeaponCard[] weapons = GameData.Instance.AllCombineWeapon;
        for (int i = 0; i < weapons.Length; i++)
        {
            UICatalogWeaponItem item;
            item = Instantiate(weaponItemPrefabe, weaponItemParent);
            item.catalogType = CatalogItemType.Weapon;
            item.gameObject.SetActive(true);
            item.ItemSetData(weapons[i], ClickWeapon);
            item.SetLockState();
        }
        LayoutRebuilder.ForceRebuildLayoutImmediate(weaponItemParent);
        size.enabled = false;
        weaponItemParent.sizeDelta = new Vector2(weaponItemParent.sizeDelta.x, 6470.547f);
    }

    private void InitCatalogFollower()//随从
    {
        hasFollowerPanelInit = true;
        List<FollowerCard> characters = GameData.Instance.AllFollowers;
        for (int i = 0; i < characters.Count; i++)
        {
            UICatalogWeaponItem item;
            item = Instantiate(followerItemPrefabe, followerItemParent);
            item.catalogType = CatalogItemType.Follower;
            item.gameObject.SetActive(true);
            item.ItemSetData(characters[i], ClickFollower);
            item.SetLockState();

        }
        LayoutRebuilder.ForceRebuildLayoutImmediate((RectTransform)followerItemParent);
    }

    private void InitCatalogbless()//祝福
    {
        hasBlessPanelInit = true;
        //CombineWeaponCard[] characters = GameData.Instance.AllCombineWeapon;
        //for (int i = 0; i < characters.Length; i++)
        //{
        //    UICatalogWeaponItem item;
        //    item = Instantiate(weaponItemPrefabe, weaponItemParent);
        //    item.gameObject.SetActive(true);
        //    item.ItemSetData(characters[i], ClickWeapon);
        //    item.SetLockState();
        //}
        LayoutRebuilder.ForceRebuildLayoutImmediate((RectTransform)blessItemParent);
    }


    public override void Setup()
    {
        base.Setup();
        InitCatalogWeapon();
        SetSelect();
        ShowPanel(0);
        hasFollowerPanelInit = false;
        hasBlessPanelInit = false;
    }

    public void ClickWeapon(UICatalogItemBase item)
    {
        UIManagers.Instance.AddOverlayMenu<MenuWeaponInfo>(MenuType.MenuWeaponInfo, (menu) =>
        {
            menu.InitData((CombineWeaponCard)item.card, RunData.ins);
        });
    }

    public void ClickFollower(UICatalogItemBase item)
    {
        //UIManagers.Instance.GetMenu<UIFollowerInfo>(UIMenuBase.MenuType.MenuFollowerInfo).Init(item.card);
        //UIManagers.Instance.AddOverlayMenu<UIFollowerInfo>(UIMenuBase.MenuType.MenuFollowerInfo,menu=> {
        //    menu.Init(item.card);
        //});
        UIManagers.Instance.AddOverlayMenu<UIFollowerInfo>(MenuType.MenuFollowerInfo, (menu) =>
        {
            menu.Init(item.card);
        });
        //uIFollowerInfo.gameObject.SetActive(true);
        //uIFollowerInfo.Init(item.card);
    }

    public void ClickBless(UICatalogItemBase item)
    {

    }

    private void SetSelect()
    {

        for (int i = 0; i < Btns.Length; i++)
        {
            if (i == CurSelectIndex)
                Btns[i].SetState(1);
            else
                Btns[i].SetState(0);
        }
    }

    public void ChangePanel(int index)
    {
        for (int i = 0; i < panels.Count; i++)
        {
            panels[i].SetActive(index == i);
        }
        bg.sprite = panelBgs[index];
        //收集进度修改
    }

    public void OnClickBtns(int Index)
    {
        if (Index != CurSelectIndex)
        {
            ChangePanel(Index);
            CurSelectIndex = Index;
            SetSelect();
        }
    }

    public void ShowPanel(int index)
    {
        bool isInGame = SceneMgr.isInGame;
        btnsParent.SetActive(!isInGame);
        progressParent.SetActive(!isInGame);
        closeButton.SetActive(isInGame);

        // 更新选中状态
        CurSelectIndex = index;
        ChangePanel(index);
        SetSelect(); // 添加这行来更新按钮的选中状态

        if (index == 1 && !hasFollowerPanelInit)
        {
            InitCatalogFollower();
        }
        else if (index == 2 && !hasBlessPanelInit)
        {
            InitCatalogbless();
        }
    }


}
