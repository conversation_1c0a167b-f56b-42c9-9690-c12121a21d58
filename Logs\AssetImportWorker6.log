Using pre-set license
Built from '2021.3/china_unity/release' branch; Version is '2021.3.21f1c1 (08fa194de70f) revision 588313'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'zh' Physical Memory: 32717 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\Program Files (x86)\2021.3.21f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker6
-projectPath
G:/GAME_ZD/Brotato_LegionSurvivors
-logFile
Logs/AssetImportWorker6.log
-srvPort
9550
Successfully changed project path to: G:/GAME_ZD/Brotato_LegionSurvivors
G:/GAME_ZD/Brotato_LegionSurvivors
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [168604] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 4245896125 [EditorId] 4245896125 [Version] 1048832 [Id] WindowsEditor(7,MS-TFNUFGBLYRYF) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [168604] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 4245896125 [EditorId] 4245896125 [Version] 1048832 [Id] WindowsEditor(7,MS-TFNUFGBLYRYF) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

AS: AutoStreaming module initializing.
Refreshing native plugins compatible for Editor in 247.87 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.21f1c1 (08fa194de70f)
[Subsystems] Discovering subsystems at path D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path G:/GAME_ZD/Brotato_LegionSurvivors/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1660 SUPER (ID=0x21c4)
    Vendor:   NVIDIA
    VRAM:     5966 MB
    Driver:   32.0.15.6094
Initialize mono
Mono path[0] = 'D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed'
Mono path[1] = 'D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56804
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.005190 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 390 ms
Refreshing native plugins compatible for Editor in 224.54 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.374 seconds
Domain Reload Profiling:
	ReloadAssembly (1375ms)
		BeginReloadAssembly (99ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (1149ms)
			LoadAssemblies (96ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (140ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (35ms)
			SetupLoadedEditorAssemblies (896ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (469ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (225ms)
				BeforeProcessingInitializeOnLoad (2ms)
				ProcessInitializeOnLoadAttributes (118ms)
				ProcessInitializeOnLoadMethodAttributes (82ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.017631 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 213.70 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.644 seconds
Domain Reload Profiling:
	ReloadAssembly (1646ms)
		BeginReloadAssembly (143ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (26ms)
		EndReloadAssembly (1354ms)
			LoadAssemblies (106ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (261ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (98ms)
			SetupLoadedEditorAssemblies (824ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (21ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (214ms)
				BeforeProcessingInitializeOnLoad (91ms)
				ProcessInitializeOnLoadAttributes (435ms)
				ProcessInitializeOnLoadMethodAttributes (54ms)
				AfterProcessingInitializeOnLoad (10ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (7ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.09 seconds
Refreshing native plugins compatible for Editor in 3.10 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3276 Unused Serialized files (Serialized files now loaded: 0)
Unloading 130 unused Assets / (134.7 KB). Loaded Objects now: 3686.
Memory consumption went from 226.8 MB to 226.6 MB.
Total: 4.906300 ms (FindLiveObjects: 0.386500 ms CreateObjectMapping: 0.091600 ms MarkObjects: 4.201600 ms  DeleteObjects: 0.225000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 1471700.443906 seconds.
  path: Assets/Resources/Prefabs/Enemies/Monster_Bruiser 1.prefab
  artifactKey: Guid(497bd359de5bc7b4383f8f07f95ac743) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Enemies/Monster_Bruiser 1.prefab using Guid(497bd359de5bc7b4383f8f07f95ac743) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '09b0b0d56b8d0c7b738b7cf56018e439') in 0.078104 seconds 
========================================================================
Received Import Request.
  Time since last request: 5.485134 seconds.
  path: Assets/Resources/Prefabs/Enemies/Monster_Crown Slime.prefab
  artifactKey: Guid(497bd359de5bc7b4383f8f07f95ac743) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Enemies/Monster_Crown Slime.prefab using Guid(497bd359de5bc7b4383f8f07f95ac743) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ecd41c1836f07b861d0938cd5119ec85') in 0.003877 seconds 
========================================================================
Received Import Request.
  Time since last request: 8.937340 seconds.
  path: Assets/Resources/Prefabs/Enemies/Monster_Crown Slime.prefab
  artifactKey: Guid(497bd359de5bc7b4383f8f07f95ac743) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Enemies/Monster_Crown Slime.prefab using Guid(497bd359de5bc7b4383f8f07f95ac743) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'cf1c8503fcd17965891b2e968483f81d') in 0.004169 seconds 
========================================================================
Received Import Request.
  Time since last request: 61.523490 seconds.
  path: Assets/Resources/Prefabs/Enemies/Monster_Crown Slime.prefab
  artifactKey: Guid(497bd359de5bc7b4383f8f07f95ac743) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Enemies/Monster_Crown Slime.prefab using Guid(497bd359de5bc7b4383f8f07f95ac743) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '62df5245117663a2d53c93a0dd6ba3c3') in 0.003735 seconds 
========================================================================
Received Import Request.
  Time since last request: 2101.059522 seconds.
  path: Assets/Resources/Prefabs/Enemies/Monster_CrownSlime.prefab
  artifactKey: Guid(497bd359de5bc7b4383f8f07f95ac743) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Enemies/Monster_CrownSlime.prefab using Guid(497bd359de5bc7b4383f8f07f95ac743) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8dad24fb0e2c4e59417ae31c34a42864') in 0.003852 seconds 
========================================================================
Received Import Request.
  Time since last request: 939.043008 seconds.
  path: Assets/ScenesPortrait_dungeon/UIMenus/UIGamePlayerUI.prefab
  artifactKey: Guid(f775e98860395d34189c2cb9379b47c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ScenesPortrait_dungeon/UIMenus/UIGamePlayerUI.prefab using Guid(f775e98860395d34189c2cb9379b47c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e0cff72389402b202895ce96ad3a41e6') in 0.048911 seconds 
========================================================================
Received Import Request.
  Time since last request: 6.836221 seconds.
  path: Assets/Scenes/UIMenus/UIGamePlayerUI.prefab
  artifactKey: Guid(ae43eff9d7e7ce24b8b9cb9db8b53b24) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/UIMenus/UIGamePlayerUI.prefab using Guid(ae43eff9d7e7ce24b8b9cb9db8b53b24) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd91826bfbb55ad49b739dea75be0521c') in 0.014612 seconds 
========================================================================
Received Import Request.
  Time since last request: 4.493609 seconds.
  path: Assets/Scenes_dungeon/UIMenus/UIGamePlayerUI.prefab
  artifactKey: Guid(88bbe8b726f185148b49fa4eab8f98c3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes_dungeon/UIMenus/UIGamePlayerUI.prefab using Guid(88bbe8b726f185148b49fa4eab8f98c3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '71a8904ab695d936c0330c0b68527ab9') in 0.015305 seconds 
========================================================================
Received Import Request.
  Time since last request: 183.264037 seconds.
  path: Assets/ScenesPortrait_dungeon/UIMenus/MenuGame.prefab
  artifactKey: Guid(20af5b007443b604f96d8700a1a9703e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ScenesPortrait_dungeon/UIMenus/MenuGame.prefab using Guid(20af5b007443b604f96d8700a1a9703e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b3564683dd83adc536f805904ad1cdc8') in 0.016523 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014512 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 4.39 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.540 seconds
Domain Reload Profiling:
	ReloadAssembly (1541ms)
		BeginReloadAssembly (193ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (46ms)
		EndReloadAssembly (1206ms)
			LoadAssemblies (128ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (314ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (64ms)
			SetupLoadedEditorAssemblies (525ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (4ms)
				BeforeProcessingInitializeOnLoad (87ms)
				ProcessInitializeOnLoadAttributes (338ms)
				ProcessInitializeOnLoadMethodAttributes (60ms)
				AfterProcessingInitializeOnLoad (10ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (107ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 18.05 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3218 Unused Serialized files (Serialized files now loaded: 0)
Unloading 106 unused Assets / (101.4 KB). Loaded Objects now: 3704.
Memory consumption went from 221.4 MB to 221.3 MB.
Total: 6.041900 ms (FindLiveObjects: 0.455700 ms CreateObjectMapping: 0.306700 ms MarkObjects: 5.103500 ms  DeleteObjects: 0.174200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 109.136964 seconds.
  path: Assets/ScenesPortrait_dungeon/UIMenus/MenuGame.prefab
  artifactKey: Guid(20af5b007443b604f96d8700a1a9703e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ScenesPortrait_dungeon/UIMenus/MenuGame.prefab using Guid(20af5b007443b604f96d8700a1a9703e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6192623e33358a35b6e0040a8d21c832') in 0.061427 seconds 
========================================================================
Received Import Request.
  Time since last request: 45.471420 seconds.
  path: Assets/ScenesPortrait_dungeon/UIMenus/UIGamePlayerUI.prefab
  artifactKey: Guid(f775e98860395d34189c2cb9379b47c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ScenesPortrait_dungeon/UIMenus/UIGamePlayerUI.prefab using Guid(f775e98860395d34189c2cb9379b47c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e76e328d1340fb7bbf7026cf3ebafc5a') in 0.030455 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015446 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 4.85 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.546 seconds
Domain Reload Profiling:
	ReloadAssembly (1547ms)
		BeginReloadAssembly (177ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (43ms)
		EndReloadAssembly (1223ms)
			LoadAssemblies (127ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (294ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (68ms)
			SetupLoadedEditorAssemblies (546ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (5ms)
				BeforeProcessingInitializeOnLoad (87ms)
				ProcessInitializeOnLoadAttributes (358ms)
				ProcessInitializeOnLoadMethodAttributes (60ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (107ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 19.52 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3218 Unused Serialized files (Serialized files now loaded: 0)
Unloading 106 unused Assets / (101.5 KB). Loaded Objects now: 3719.
Memory consumption went from 221.4 MB to 221.3 MB.
Total: 5.783800 ms (FindLiveObjects: 0.613000 ms CreateObjectMapping: 0.098600 ms MarkObjects: 4.944100 ms  DeleteObjects: 0.126300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016468 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 3.53 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.609 seconds
Domain Reload Profiling:
	ReloadAssembly (1610ms)
		BeginReloadAssembly (173ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (41ms)
		EndReloadAssembly (1299ms)
			LoadAssemblies (118ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (309ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (85ms)
			SetupLoadedEditorAssemblies (589ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (4ms)
				BeforeProcessingInitializeOnLoad (96ms)
				ProcessInitializeOnLoadAttributes (396ms)
				ProcessInitializeOnLoadMethodAttributes (58ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (102ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 19.97 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3218 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (100.7 KB). Loaded Objects now: 3734.
Memory consumption went from 221.4 MB to 221.3 MB.
Total: 4.265100 ms (FindLiveObjects: 0.317400 ms CreateObjectMapping: 0.083500 ms MarkObjects: 3.772800 ms  DeleteObjects: 0.089400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 557.797160 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet_Hammer01.prefab
  artifactKey: Guid(2b449adb8c00eec4fa126163d7ff973a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet_Hammer01.prefab using Guid(2b449adb8c00eec4fa126163d7ff973a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '443fff1ccbb133a4700fe102650b5306') in 0.127359 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014517 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 3.12 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.542 seconds
Domain Reload Profiling:
	ReloadAssembly (1543ms)
		BeginReloadAssembly (180ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (45ms)
		EndReloadAssembly (1215ms)
			LoadAssemblies (119ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (298ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (69ms)
			SetupLoadedEditorAssemblies (531ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (3ms)
				BeforeProcessingInitializeOnLoad (97ms)
				ProcessInitializeOnLoadAttributes (337ms)
				ProcessInitializeOnLoadMethodAttributes (60ms)
				AfterProcessingInitializeOnLoad (10ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (111ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 16.99 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3218 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (100.7 KB). Loaded Objects now: 3756.
Memory consumption went from 225.8 MB to 225.7 MB.
Total: 5.856600 ms (FindLiveObjects: 0.559200 ms CreateObjectMapping: 0.189300 ms MarkObjects: 5.021900 ms  DeleteObjects: 0.084400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 122.405468 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet_Hammer01.prefab
  artifactKey: Guid(2b449adb8c00eec4fa126163d7ff973a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet_Hammer01.prefab using Guid(2b449adb8c00eec4fa126163d7ff973a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'aa5ab0b06dac0012be25f8bb4a18304d') in 0.081712 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.018474 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 3.50 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.485 seconds
Domain Reload Profiling:
	ReloadAssembly (1486ms)
		BeginReloadAssembly (174ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (42ms)
		EndReloadAssembly (1163ms)
			LoadAssemblies (123ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (290ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (67ms)
			SetupLoadedEditorAssemblies (508ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (22ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (4ms)
				BeforeProcessingInitializeOnLoad (82ms)
				ProcessInitializeOnLoadAttributes (333ms)
				ProcessInitializeOnLoadMethodAttributes (58ms)
				AfterProcessingInitializeOnLoad (10ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (100ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 18.61 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3218 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (100.7 KB). Loaded Objects now: 3771.
Memory consumption went from 225.8 MB to 225.7 MB.
Total: 4.841000 ms (FindLiveObjects: 0.489900 ms CreateObjectMapping: 0.114900 ms MarkObjects: 4.103500 ms  DeleteObjects: 0.130800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 321.288695 seconds.
  path: Assets/Resources/Prefabs/Weapon/Weapon_Hammer01.prefab
  artifactKey: Guid(aed3fa522cfae92488e3de77248f89bc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Weapon/Weapon_Hammer01.prefab using Guid(aed3fa522cfae92488e3de77248f89bc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '88c0ecff99b588fa882ec1a3715c8d3c') in 0.085274 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.560258 seconds.
  path: Assets/GameResources/Dungeon/DragonBonesData/Weapon_Hammer01/Weapon_Hammer01_ske.json
  artifactKey: Guid(1a738b7385bf7dd4993658fb44aa6e3e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameResources/Dungeon/DragonBonesData/Weapon_Hammer01/Weapon_Hammer01_ske.json using Guid(1a738b7385bf7dd4993658fb44aa6e3e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '18792fd3a652732d7216cacfaa35f8a9') in 0.016538 seconds 
========================================================================
Received Import Request.
  Time since last request: 1.092998 seconds.
  path: Assets/GameResources/Dungeon/DragonBonesData/Weapon_Hammer01/Weapon_Hammer01_tex.json
  artifactKey: Guid(504f04eaf2cfffb4a804e3cd01763a32) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameResources/Dungeon/DragonBonesData/Weapon_Hammer01/Weapon_Hammer01_tex.json using Guid(504f04eaf2cfffb4a804e3cd01763a32) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '33f66cc766b6c0b8bc940a393ecb6115') in 0.002331 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.017404 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 6.42 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.501 seconds
Domain Reload Profiling:
	ReloadAssembly (1501ms)
		BeginReloadAssembly (177ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (43ms)
		EndReloadAssembly (1189ms)
			LoadAssemblies (130ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (288ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (64ms)
			SetupLoadedEditorAssemblies (533ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (7ms)
				BeforeProcessingInitializeOnLoad (88ms)
				ProcessInitializeOnLoadAttributes (344ms)
				ProcessInitializeOnLoadMethodAttributes (59ms)
				AfterProcessingInitializeOnLoad (10ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (102ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 18.44 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3218 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (100.7 KB). Loaded Objects now: 3786.
Memory consumption went from 225.8 MB to 225.7 MB.
Total: 9.831300 ms (FindLiveObjects: 1.410000 ms CreateObjectMapping: 0.174600 ms MarkObjects: 7.943300 ms  DeleteObjects: 0.300700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 426.935450 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet_Hammer01.prefab
  artifactKey: Guid(2b449adb8c00eec4fa126163d7ff973a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet_Hammer01.prefab using Guid(2b449adb8c00eec4fa126163d7ff973a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5ac7a35adc1274bb39632b91591e7818') in 0.082901 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.018677 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 3.44 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.544 seconds
Domain Reload Profiling:
	ReloadAssembly (1544ms)
		BeginReloadAssembly (176ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (41ms)
		EndReloadAssembly (1223ms)
			LoadAssemblies (127ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (293ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (67ms)
			SetupLoadedEditorAssemblies (545ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (28ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (4ms)
				BeforeProcessingInitializeOnLoad (89ms)
				ProcessInitializeOnLoadAttributes (351ms)
				ProcessInitializeOnLoadMethodAttributes (62ms)
				AfterProcessingInitializeOnLoad (10ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (118ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 20.63 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3218 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (100.8 KB). Loaded Objects now: 3801.
Memory consumption went from 225.9 MB to 225.8 MB.
Total: 6.566300 ms (FindLiveObjects: 1.374200 ms CreateObjectMapping: 0.168000 ms MarkObjects: 4.917600 ms  DeleteObjects: 0.104800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 273.090329 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet_Hammer01.prefab
  artifactKey: Guid(2b449adb8c00eec4fa126163d7ff973a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet_Hammer01.prefab using Guid(2b449adb8c00eec4fa126163d7ff973a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6e446ba696ff4c4a45b9751630005d65') in 0.081898 seconds 
========================================================================
Received Import Request.
  Time since last request: 27.280198 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet_Hammer01.prefab
  artifactKey: Guid(2b449adb8c00eec4fa126163d7ff973a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet_Hammer01.prefab using Guid(2b449adb8c00eec4fa126163d7ff973a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1f7a09839a6ce3d79422a08e1745f58b') in 0.016408 seconds 
========================================================================
Received Import Request.
  Time since last request: 3.212667 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet_Hammer02.prefab
  artifactKey: Guid(20d61c409db3f9c40a47f77d4c7d1525) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet_Hammer02.prefab using Guid(20d61c409db3f9c40a47f77d4c7d1525) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2ad1d94b89dae648e7371fbcd2fc22da') in 0.015844 seconds 
========================================================================
Received Import Request.
  Time since last request: 26.219050 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet_Hammer03.prefab
  artifactKey: Guid(66ca950e126e1db44ad3b1ec658bf3c7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet_Hammer03.prefab using Guid(66ca950e126e1db44ad3b1ec658bf3c7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ba7bf0f43c308b3ca1213eca1f613c4b') in 0.016169 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.017369 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 4.37 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.548 seconds
Domain Reload Profiling:
	ReloadAssembly (1549ms)
		BeginReloadAssembly (172ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (41ms)
		EndReloadAssembly (1229ms)
			LoadAssemblies (118ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (300ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (69ms)
			SetupLoadedEditorAssemblies (549ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (4ms)
				BeforeProcessingInitializeOnLoad (93ms)
				ProcessInitializeOnLoadAttributes (350ms)
				ProcessInitializeOnLoadMethodAttributes (66ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (105ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 17.77 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3218 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (100.7 KB). Loaded Objects now: 3816.
Memory consumption went from 225.9 MB to 225.8 MB.
Total: 4.967800 ms (FindLiveObjects: 0.436600 ms CreateObjectMapping: 0.115600 ms MarkObjects: 4.297900 ms  DeleteObjects: 0.116400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 158.010452 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet_Shield01.prefab
  artifactKey: Guid(bb8f3b59f8fb6144db3cb89ba98c523d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet_Shield01.prefab using Guid(bb8f3b59f8fb6144db3cb89ba98c523d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5bc0bd89998bd54cba37cdb53e510940') in 0.118236 seconds 
========================================================================
Received Import Request.
  Time since last request: 34.105489 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet_Shield02.prefab
  artifactKey: Guid(dc341fea41d3a4a4abe36046d80b6fe2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet_Shield02.prefab using Guid(dc341fea41d3a4a4abe36046d80b6fe2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8c6c7e270b484347b660ea24bdecac8e') in 0.013003 seconds 
========================================================================
Received Import Request.
  Time since last request: 24.783060 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet_Shield03.prefab
  artifactKey: Guid(0631c80455998a7469f2c3163bc67758) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet_Shield03.prefab using Guid(0631c80455998a7469f2c3163bc67758) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'abbc471ecdaa89e12e07290e20a8b058') in 0.012514 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014571 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 4.93 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.524 seconds
Domain Reload Profiling:
	ReloadAssembly (1525ms)
		BeginReloadAssembly (174ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (40ms)
		EndReloadAssembly (1188ms)
			LoadAssemblies (124ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (275ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (70ms)
			SetupLoadedEditorAssemblies (536ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (5ms)
				BeforeProcessingInitializeOnLoad (85ms)
				ProcessInitializeOnLoadAttributes (350ms)
				ProcessInitializeOnLoadMethodAttributes (59ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (1ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (109ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 18.36 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3218 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (100.7 KB). Loaded Objects now: 3831.
Memory consumption went from 225.9 MB to 225.8 MB.
Total: 4.468500 ms (FindLiveObjects: 0.461300 ms CreateObjectMapping: 0.109500 ms MarkObjects: 3.813500 ms  DeleteObjects: 0.082600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 401.883155 seconds.
  path: Assets/Resources/Prefabs/Weapon/Weapon_Shield01.prefab
  artifactKey: Guid(03e4694faf4801543b15eefdbe19af32) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Weapon/Weapon_Shield01.prefab using Guid(03e4694faf4801543b15eefdbe19af32) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'dd79775418204717cfa6ef11aa5b30c2') in 0.074494 seconds 
========================================================================
Received Import Request.
  Time since last request: 2.770117 seconds.
  path: Assets/Resources/Prefabs/Weapon/Weapon_Shield02.prefab
  artifactKey: Guid(9a3cd85c5cc39bf489003f77a1fa64c8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Weapon/Weapon_Shield02.prefab using Guid(9a3cd85c5cc39bf489003f77a1fa64c8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3b9b01dd32535f405f75e329a38e333b') in 0.019622 seconds 
========================================================================
Received Import Request.
  Time since last request: 2.947406 seconds.
  path: Assets/Resources/Prefabs/Weapon/Weapon_Shield02.prefab
  artifactKey: Guid(9a3cd85c5cc39bf489003f77a1fa64c8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Weapon/Weapon_Shield02.prefab using Guid(9a3cd85c5cc39bf489003f77a1fa64c8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0624b57c75198b862b209fc5d49d2f0f') in 0.014830 seconds 
========================================================================
Received Import Request.
  Time since last request: 5.348464 seconds.
  path: Assets/Resources/Prefabs/Weapon/Weapon_Shield01.prefab
  artifactKey: Guid(03e4694faf4801543b15eefdbe19af32) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Weapon/Weapon_Shield01.prefab using Guid(03e4694faf4801543b15eefdbe19af32) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd14466dbdd20800dbe119791a08feb35') in 0.015023 seconds 
========================================================================
Received Import Request.
  Time since last request: 14.334155 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet_Shield01.prefab
  artifactKey: Guid(bb8f3b59f8fb6144db3cb89ba98c523d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet_Shield01.prefab using Guid(bb8f3b59f8fb6144db3cb89ba98c523d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '323e893fb9e23c58c02cf3dc192e8dce') in 0.016154 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014820 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 3.02 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.572 seconds
Domain Reload Profiling:
	ReloadAssembly (1573ms)
		BeginReloadAssembly (175ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (41ms)
		EndReloadAssembly (1256ms)
			LoadAssemblies (123ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (307ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (80ms)
			SetupLoadedEditorAssemblies (534ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (26ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (3ms)
				BeforeProcessingInitializeOnLoad (88ms)
				ProcessInitializeOnLoadAttributes (348ms)
				ProcessInitializeOnLoadMethodAttributes (59ms)
				AfterProcessingInitializeOnLoad (10ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (109ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 17.29 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3218 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (100.7 KB). Loaded Objects now: 3846.
Memory consumption went from 225.9 MB to 225.8 MB.
Total: 4.532400 ms (FindLiveObjects: 0.352500 ms CreateObjectMapping: 0.086200 ms MarkObjects: 4.009100 ms  DeleteObjects: 0.083200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 229.699959 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet_Shield02.prefab
  artifactKey: Guid(dc341fea41d3a4a4abe36046d80b6fe2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet_Shield02.prefab using Guid(dc341fea41d3a4a4abe36046d80b6fe2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5312cf41013c77945a1ca16a4e4d8615') in 0.078170 seconds 
========================================================================
Received Import Request.
  Time since last request: 8.783815 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet_Shield03.prefab
  artifactKey: Guid(0631c80455998a7469f2c3163bc67758) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet_Shield03.prefab using Guid(0631c80455998a7469f2c3163bc67758) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'cac5de9e472126e05766ce1e661f59d8') in 0.012432 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015214 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 3.87 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.627 seconds
Domain Reload Profiling:
	ReloadAssembly (1628ms)
		BeginReloadAssembly (179ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (43ms)
		EndReloadAssembly (1298ms)
			LoadAssemblies (121ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (301ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (62ms)
			SetupLoadedEditorAssemblies (634ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (4ms)
				BeforeProcessingInitializeOnLoad (178ms)
				ProcessInitializeOnLoadAttributes (354ms)
				ProcessInitializeOnLoadMethodAttributes (62ms)
				AfterProcessingInitializeOnLoad (10ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (106ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 17.73 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3218 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (100.6 KB). Loaded Objects now: 3861.
Memory consumption went from 226.0 MB to 225.9 MB.
Total: 4.372500 ms (FindLiveObjects: 0.502400 ms CreateObjectMapping: 0.115100 ms MarkObjects: 3.645000 ms  DeleteObjects: 0.108200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 775.831650 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet _Blade01.prefab
  artifactKey: Guid(096f0c3671974da4daf9c04d3cd81fc1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet _Blade01.prefab using Guid(096f0c3671974da4daf9c04d3cd81fc1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'edfe0411c9ed0f87c48bbd464a6c452c') in 0.091034 seconds 
========================================================================
Received Import Request.
  Time since last request: 12.811677 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet _Blade02.prefab
  artifactKey: Guid(958dcd6c03a970441948e565a322e97f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet _Blade02.prefab using Guid(958dcd6c03a970441948e565a322e97f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fc9f3ba2279674274a40125720b54776') in 0.013390 seconds 
========================================================================
Received Import Request.
  Time since last request: 10.805713 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet _Blade03.prefab
  artifactKey: Guid(a6979ab2550d7f549a147929499f4a67) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet _Blade03.prefab using Guid(a6979ab2550d7f549a147929499f4a67) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '70ea6bd20e9bdfdb05cb0f440fc7e098') in 0.018049 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014073 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 4.25 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.525 seconds
Domain Reload Profiling:
	ReloadAssembly (1525ms)
		BeginReloadAssembly (181ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (40ms)
		EndReloadAssembly (1203ms)
			LoadAssemblies (131ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (284ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (68ms)
			SetupLoadedEditorAssemblies (540ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (27ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (4ms)
				BeforeProcessingInitializeOnLoad (95ms)
				ProcessInitializeOnLoadAttributes (343ms)
				ProcessInitializeOnLoadMethodAttributes (60ms)
				AfterProcessingInitializeOnLoad (10ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (104ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 30.27 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3218 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (100.6 KB). Loaded Objects now: 3876.
Memory consumption went from 226.0 MB to 225.9 MB.
Total: 5.686500 ms (FindLiveObjects: 0.551000 ms CreateObjectMapping: 0.323800 ms MarkObjects: 4.716800 ms  DeleteObjects: 0.093400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 57.075783 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet _Blade03.prefab
  artifactKey: Guid(a6979ab2550d7f549a147929499f4a67) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet _Blade03.prefab using Guid(a6979ab2550d7f549a147929499f4a67) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c86fa8557a1337494e3e24b3e02c0af2') in 0.080699 seconds 
========================================================================
Received Import Request.
  Time since last request: 34.724418 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectileBolt_HeartArrow03.prefab
  artifactKey: Guid(5250061de115e0d49b57790bf021351e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectileBolt_HeartArrow03.prefab using Guid(5250061de115e0d49b57790bf021351e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fb5733ea7202bc114f5d93f0df0b386a') in 0.024085 seconds 
========================================================================
Received Import Request.
  Time since last request: 51.203193 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectileBolt_HeartArrow01.prefab
  artifactKey: Guid(6ca17a9828968804cac01f910758f834) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectileBolt_HeartArrow01.prefab using Guid(6ca17a9828968804cac01f910758f834) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '86c420be305caf743f4ea892a1ec663f') in 0.020172 seconds 
========================================================================
Received Import Request.
  Time since last request: 10.898396 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet _Blade01.prefab
  artifactKey: Guid(096f0c3671974da4daf9c04d3cd81fc1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet _Blade01.prefab using Guid(096f0c3671974da4daf9c04d3cd81fc1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e5c8a69395dbf966d4a02e0df88c0403') in 0.012977 seconds 
========================================================================
Received Import Request.
  Time since last request: 3.417687 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet _Blade1.prefab
  artifactKey: Guid(d00af8bcb0dbded4e9773b1345356f85) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet _Blade1.prefab using Guid(d00af8bcb0dbded4e9773b1345356f85) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '151ff586ff85189b83d08fedbcd05926') in 0.012433 seconds 
========================================================================
Received Import Request.
  Time since last request: 10.351596 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet _Kingblade01.prefab
  artifactKey: Guid(b2193248fd4106e459d0180b68165542) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet _Kingblade01.prefab using Guid(b2193248fd4106e459d0180b68165542) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '59817a3b82af8fe97e0ad7042ecb5529') in 0.092185 seconds 
========================================================================
Received Import Request.
  Time since last request: 17.207411 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet _Kingblade1.prefab
  artifactKey: Guid(f852339bdcca2d84db121718983751ff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet _Kingblade1.prefab using Guid(f852339bdcca2d84db121718983751ff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2cdecfeecb88bf022fcc125d6bf7760b') in 0.019479 seconds 
========================================================================
Received Import Request.
  Time since last request: 5.999462 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet _Kingblade02.prefab
  artifactKey: Guid(867f7b2509706ac4f89dd1d03e749b12) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet _Kingblade02.prefab using Guid(867f7b2509706ac4f89dd1d03e749b12) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '44abaf7a95841fbb560d09812be6bb02') in 0.012097 seconds 
========================================================================
Received Import Request.
  Time since last request: 9.143806 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet_Shield01.prefab
  artifactKey: Guid(bb8f3b59f8fb6144db3cb89ba98c523d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet_Shield01.prefab using Guid(bb8f3b59f8fb6144db3cb89ba98c523d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7b16e86a7bbd97599ec1fc9b091d9a10') in 0.013038 seconds 
========================================================================
Received Import Request.
  Time since last request: 33.143791 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet_Shield02.prefab
  artifactKey: Guid(dc341fea41d3a4a4abe36046d80b6fe2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet_Shield02.prefab using Guid(dc341fea41d3a4a4abe36046d80b6fe2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f687407199839813eaab85259428595a') in 0.012688 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.908742 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet_Shield03.prefab
  artifactKey: Guid(0631c80455998a7469f2c3163bc67758) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet_Shield03.prefab using Guid(0631c80455998a7469f2c3163bc67758) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8886ee15f6edfa7c81abb23a86293ccc') in 0.011799 seconds 
========================================================================
Received Import Request.
  Time since last request: 1.610028 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet_Shieldbow01.prefab
  artifactKey: Guid(4cab02827b2060847a410f5c8a134f2f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet_Shieldbow01.prefab using Guid(4cab02827b2060847a410f5c8a134f2f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5af42645ebb3f06724fa5ebf205b045a') in 0.015847 seconds 
========================================================================
Received Import Request.
  Time since last request: 20.296611 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet_Shieldbow02.prefab
  artifactKey: Guid(d74cd4d613a9b8746b3b7f5188856bcb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet_Shieldbow02.prefab using Guid(d74cd4d613a9b8746b3b7f5188856bcb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ba2b84a3854510d784a2ded25b3efb71') in 0.017646 seconds 
========================================================================
Received Import Request.
  Time since last request: 8.926744 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet_SoulScythe01.prefab
  artifactKey: Guid(05afab6e1d0387d4790a1531543cc1dc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet_SoulScythe01.prefab using Guid(05afab6e1d0387d4790a1531543cc1dc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a22f6bb036a803ccb47affdfb3abe81d') in 0.010980 seconds 
========================================================================
Received Import Request.
  Time since last request: 21.200148 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet_Wrench01.prefab
  artifactKey: Guid(856ef445fbbc284459216477a6cf7762) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet_Wrench01.prefab using Guid(856ef445fbbc284459216477a6cf7762) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '52e54d86c109ba7de61d29cf8e39c497') in 0.010648 seconds 
========================================================================
Received Import Request.
  Time since last request: 4.371524 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet_Wrench02.prefab
  artifactKey: Guid(753e1a6278acf564d816b3b4e798a893) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet_Wrench02.prefab using Guid(753e1a6278acf564d816b3b4e798a893) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd264089cfb8d67f0cb25bb1765963264') in 0.011195 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016745 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 9.22 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.532 seconds
Domain Reload Profiling:
	ReloadAssembly (1533ms)
		BeginReloadAssembly (174ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (44ms)
		EndReloadAssembly (1213ms)
			LoadAssemblies (116ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (300ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (71ms)
			SetupLoadedEditorAssemblies (543ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (90ms)
				ProcessInitializeOnLoadAttributes (346ms)
				ProcessInitializeOnLoadMethodAttributes (62ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (104ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 18.82 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3218 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (100.7 KB). Loaded Objects now: 3893.
Memory consumption went from 226.1 MB to 226.0 MB.
Total: 5.479400 ms (FindLiveObjects: 0.527800 ms CreateObjectMapping: 0.107700 ms MarkObjects: 4.623600 ms  DeleteObjects: 0.217800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015187 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 4.13 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.595 seconds
Domain Reload Profiling:
	ReloadAssembly (1596ms)
		BeginReloadAssembly (173ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (41ms)
		EndReloadAssembly (1275ms)
			LoadAssemblies (114ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (359ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (76ms)
			SetupLoadedEditorAssemblies (544ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (22ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (4ms)
				BeforeProcessingInitializeOnLoad (92ms)
				ProcessInitializeOnLoadAttributes (350ms)
				ProcessInitializeOnLoadMethodAttributes (65ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (104ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 19.67 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3218 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (100.8 KB). Loaded Objects now: 3908.
Memory consumption went from 226.1 MB to 226.0 MB.
Total: 5.229200 ms (FindLiveObjects: 0.632000 ms CreateObjectMapping: 0.117800 ms MarkObjects: 4.366200 ms  DeleteObjects: 0.111200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.021709 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 3.31 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.535 seconds
Domain Reload Profiling:
	ReloadAssembly (1536ms)
		BeginReloadAssembly (173ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (41ms)
		EndReloadAssembly (1214ms)
			LoadAssemblies (118ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (292ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (65ms)
			SetupLoadedEditorAssemblies (540ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (3ms)
				BeforeProcessingInitializeOnLoad (90ms)
				ProcessInitializeOnLoadAttributes (346ms)
				ProcessInitializeOnLoadMethodAttributes (65ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (101ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 19.53 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3218 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (100.7 KB). Loaded Objects now: 3923.
Memory consumption went from 226.1 MB to 226.0 MB.
Total: 4.996100 ms (FindLiveObjects: 0.395500 ms CreateObjectMapping: 0.083200 ms MarkObjects: 4.411400 ms  DeleteObjects: 0.104300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016880 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 4.66 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.860 seconds
Domain Reload Profiling:
	ReloadAssembly (1864ms)
		BeginReloadAssembly (200ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (48ms)
		EndReloadAssembly (1442ms)
			LoadAssemblies (145ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (378ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (97ms)
			SetupLoadedEditorAssemblies (581ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (27ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (5ms)
				BeforeProcessingInitializeOnLoad (105ms)
				ProcessInitializeOnLoadAttributes (360ms)
				ProcessInitializeOnLoadMethodAttributes (72ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (117ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 21.55 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3218 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (100.7 KB). Loaded Objects now: 3938.
Memory consumption went from 226.2 MB to 226.1 MB.
Total: 5.385000 ms (FindLiveObjects: 0.681700 ms CreateObjectMapping: 0.153500 ms MarkObjects: 4.471100 ms  DeleteObjects: 0.077100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.019937 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 5.73 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  2.105 seconds
Domain Reload Profiling:
	ReloadAssembly (2106ms)
		BeginReloadAssembly (196ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (48ms)
		EndReloadAssembly (1740ms)
			LoadAssemblies (136ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (319ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (104ms)
			SetupLoadedEditorAssemblies (964ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (28ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (6ms)
				BeforeProcessingInitializeOnLoad (194ms)
				ProcessInitializeOnLoadAttributes (666ms)
				ProcessInitializeOnLoadMethodAttributes (58ms)
				AfterProcessingInitializeOnLoad (10ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (121ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 18.65 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3218 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (100.6 KB). Loaded Objects now: 3953.
Memory consumption went from 226.2 MB to 226.1 MB.
Total: 5.390300 ms (FindLiveObjects: 0.435900 ms CreateObjectMapping: 0.108100 ms MarkObjects: 4.689900 ms  DeleteObjects: 0.154700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 54167.640653 seconds.
  path: Assets/Scripts/UI/Menu/MenuWeaponInfo.cs
  artifactKey: Guid(1d4c2179ece551f4ab33be8db97ce388) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/UI/Menu/MenuWeaponInfo.cs using Guid(1d4c2179ece551f4ab33be8db97ce388) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1fa201abd123f282244bf981c0a935fc') in 0.021930 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.017481 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 3.26 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.517 seconds
Domain Reload Profiling:
	ReloadAssembly (1518ms)
		BeginReloadAssembly (165ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (40ms)
		EndReloadAssembly (1210ms)
			LoadAssemblies (115ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (295ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (88ms)
			SetupLoadedEditorAssemblies (527ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (3ms)
				BeforeProcessingInitializeOnLoad (90ms)
				ProcessInitializeOnLoadAttributes (339ms)
				ProcessInitializeOnLoadMethodAttributes (59ms)
				AfterProcessingInitializeOnLoad (10ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (99ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 17.48 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3218 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (100.8 KB). Loaded Objects now: 3968.
Memory consumption went from 226.2 MB to 226.1 MB.
Total: 7.002600 ms (FindLiveObjects: 0.535200 ms CreateObjectMapping: 0.118600 ms MarkObjects: 6.221400 ms  DeleteObjects: 0.125700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.017106 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 4.12 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.498 seconds
Domain Reload Profiling:
	ReloadAssembly (1499ms)
		BeginReloadAssembly (169ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (40ms)
		EndReloadAssembly (1188ms)
			LoadAssemblies (117ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (288ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (69ms)
			SetupLoadedEditorAssemblies (530ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (26ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (4ms)
				BeforeProcessingInitializeOnLoad (85ms)
				ProcessInitializeOnLoadAttributes (342ms)
				ProcessInitializeOnLoadMethodAttributes (61ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (107ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 15.71 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3218 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (100.7 KB). Loaded Objects now: 3983.
Memory consumption went from 226.2 MB to 226.1 MB.
Total: 5.288200 ms (FindLiveObjects: 0.588500 ms CreateObjectMapping: 0.206700 ms MarkObjects: 4.382500 ms  DeleteObjects: 0.108900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.018461 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 3.69 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.534 seconds
Domain Reload Profiling:
	ReloadAssembly (1535ms)
		BeginReloadAssembly (181ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (45ms)
		EndReloadAssembly (1203ms)
			LoadAssemblies (120ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (302ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (67ms)
			SetupLoadedEditorAssemblies (519ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (4ms)
				BeforeProcessingInitializeOnLoad (90ms)
				ProcessInitializeOnLoadAttributes (331ms)
				ProcessInitializeOnLoadMethodAttributes (60ms)
				AfterProcessingInitializeOnLoad (10ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (98ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 15.99 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3218 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (100.7 KB). Loaded Objects now: 3998.
Memory consumption went from 226.2 MB to 226.1 MB.
Total: 4.450600 ms (FindLiveObjects: 0.416300 ms CreateObjectMapping: 0.101500 ms MarkObjects: 3.825000 ms  DeleteObjects: 0.106200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 1726.424176 seconds.
  path: Assets/ExcelData/brotato.xlsx
  artifactKey: Guid(53927fca77f1bf846a813e505f70e7ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ExcelData/brotato.xlsx using Guid(53927fca77f1bf846a813e505f70e7ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a4e0d990bdd11879858e4ce91fca2c67') in 0.041994 seconds 
========================================================================
Received Import Request.
  Time since last request: 66.274583 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectileBolt_BoneBow01.prefab
  artifactKey: Guid(c2d03a55e0a847440833d79a4d9f4d75) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectileBolt_BoneBow01.prefab using Guid(c2d03a55e0a847440833d79a4d9f4d75) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3bc97bec6143d1676b38c33b4dcb632f') in 0.062758 seconds 
========================================================================
Received Import Request.
  Time since last request: 33.220370 seconds.
  path: Assets/AssetKits/Effect_Tex/Dagger01 1.png
  artifactKey: Guid(2795f064f83e53f4ba72e6d5c171a17d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetKits/Effect_Tex/Dagger01 1.png using Guid(2795f064f83e53f4ba72e6d5c171a17d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b1245f7110ab0724c0cc6c40a6cd5f1d') in 0.043794 seconds 
========================================================================
Received Import Request.
  Time since last request: 19.984926 seconds.
  path: Assets/AssetKits/Effect_Tex/BoneBowshine.png
  artifactKey: Guid(2795f064f83e53f4ba72e6d5c171a17d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetKits/Effect_Tex/BoneBowshine.png using Guid(2795f064f83e53f4ba72e6d5c171a17d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '16f4e6eee74a56467b4b8adba7e13867') in 0.010605 seconds 
========================================================================
Received Import Request.
  Time since last request: 300.255904 seconds.
  path: Assets/AssetKits/Effect_Tex/BoneBowshine.png
  artifactKey: Guid(2795f064f83e53f4ba72e6d5c171a17d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetKits/Effect_Tex/BoneBowshine.png using Guid(2795f064f83e53f4ba72e6d5c171a17d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '18af1d3cdcc1e12e1f216f314a70956b') in 0.017760 seconds 
========================================================================
Received Import Request.
  Time since last request: 68.115279 seconds.
  path: Assets/AssetKits/Effect_Tex/BoneBowshine.png
  artifactKey: Guid(2795f064f83e53f4ba72e6d5c171a17d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetKits/Effect_Tex/BoneBowshine.png using Guid(2795f064f83e53f4ba72e6d5c171a17d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2e350d621882eb90a30f3526cbe35257') in 0.013712 seconds 
========================================================================
Received Import Request.
  Time since last request: 16.833358 seconds.
  path: Assets/AssetKits/Effect_Tex/BoneBowshine.png
  artifactKey: Guid(2795f064f83e53f4ba72e6d5c171a17d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetKits/Effect_Tex/BoneBowshine.png using Guid(2795f064f83e53f4ba72e6d5c171a17d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8d23a9aa8692a1f85475c268029e7104') in 0.012986 seconds 
========================================================================
Received Import Request.
  Time since last request: 305.475727 seconds.
  path: Assets/AssetKits/Effect_Tex/BoneBowshine.png
  artifactKey: Guid(2795f064f83e53f4ba72e6d5c171a17d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetKits/Effect_Tex/BoneBowshine.png using Guid(2795f064f83e53f4ba72e6d5c171a17d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '474efcc217618806679cf22e7d668b22') in 0.010917 seconds 
========================================================================
Received Import Request.
  Time since last request: 26.479996 seconds.
  path: Assets/AssetKits/Effect_Tex/BoneBowshine.png
  artifactKey: Guid(2795f064f83e53f4ba72e6d5c171a17d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetKits/Effect_Tex/BoneBowshine.png using Guid(2795f064f83e53f4ba72e6d5c171a17d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '80206493e68b0b282d05837fa3bad5dd') in 0.011003 seconds 
========================================================================
Received Import Request.
  Time since last request: 14.460880 seconds.
  path: Assets/AssetKits/Effect_Tex/BoneBowshine.png
  artifactKey: Guid(2795f064f83e53f4ba72e6d5c171a17d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetKits/Effect_Tex/BoneBowshine.png using Guid(2795f064f83e53f4ba72e6d5c171a17d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '27f542d0512674eb5e5745cc9263bd3a') in 0.009428 seconds 
========================================================================
Received Import Request.
  Time since last request: 17.484072 seconds.
  path: Assets/AssetKits/Effect_Tex/BoneBowshine.png
  artifactKey: Guid(2795f064f83e53f4ba72e6d5c171a17d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetKits/Effect_Tex/BoneBowshine.png using Guid(2795f064f83e53f4ba72e6d5c171a17d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd8298489a2ac5ff3853001d663aeeb56') in 0.022787 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.012583 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 2.89 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.219 seconds
Domain Reload Profiling:
	ReloadAssembly (1220ms)
		BeginReloadAssembly (141ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (33ms)
		EndReloadAssembly (961ms)
			LoadAssemblies (92ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (236ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (61ms)
			SetupLoadedEditorAssemblies (413ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (17ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (3ms)
				BeforeProcessingInitializeOnLoad (69ms)
				ProcessInitializeOnLoadAttributes (270ms)
				ProcessInitializeOnLoadMethodAttributes (47ms)
				AfterProcessingInitializeOnLoad (7ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (86ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 12.77 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3218 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (100.7 KB). Loaded Objects now: 4020.
Memory consumption went from 226.3 MB to 226.2 MB.
Total: 3.815100 ms (FindLiveObjects: 0.439900 ms CreateObjectMapping: 0.106600 ms MarkObjects: 3.200200 ms  DeleteObjects: 0.067100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 126.747268 seconds.
  path: Assets/AssetKits/Effect_Tex/BoneBowshine.png
  artifactKey: Guid(2795f064f83e53f4ba72e6d5c171a17d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetKits/Effect_Tex/BoneBowshine.png using Guid(2795f064f83e53f4ba72e6d5c171a17d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6bbfaa48862e7949b65a3dd180247138') in 0.114609 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.913088 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectileBolt_BoneBow01.prefab
  artifactKey: Guid(c2d03a55e0a847440833d79a4d9f4d75) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectileBolt_BoneBow01.prefab using Guid(c2d03a55e0a847440833d79a4d9f4d75) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd5f5f75246d5f764be89807ae91b5bb6') in 0.030705 seconds 
========================================================================
Received Import Request.
  Time since last request: 31.376986 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectileBolt_BoneBow01.prefab
  artifactKey: Guid(c2d03a55e0a847440833d79a4d9f4d75) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectileBolt_BoneBow01.prefab using Guid(c2d03a55e0a847440833d79a4d9f4d75) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ce357884d7013a9da6c21bb6fe19e2d9') in 0.014986 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.012628 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 3.52 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.176 seconds
Domain Reload Profiling:
	ReloadAssembly (1177ms)
		BeginReloadAssembly (139ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (29ms)
		EndReloadAssembly (923ms)
			LoadAssemblies (91ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (217ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (51ms)
			SetupLoadedEditorAssemblies (418ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (18ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (4ms)
				BeforeProcessingInitializeOnLoad (67ms)
				ProcessInitializeOnLoadAttributes (272ms)
				ProcessInitializeOnLoadMethodAttributes (48ms)
				AfterProcessingInitializeOnLoad (8ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (83ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 13.26 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3218 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (100.8 KB). Loaded Objects now: 4035.
Memory consumption went from 226.4 MB to 226.3 MB.
Total: 3.821700 ms (FindLiveObjects: 0.384100 ms CreateObjectMapping: 0.122500 ms MarkObjects: 3.227500 ms  DeleteObjects: 0.086600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 103.160625 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_MiniGun.prefab
  artifactKey: Guid(ebca0eb6bfa8310488f33931ea7f8368) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_MiniGun.prefab using Guid(ebca0eb6bfa8310488f33931ea7f8368) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4b645f9686797f9246c6be93563613c3') in 0.061155 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_Slingshot02.prefab
  artifactKey: Guid(55e8ea4681c4e604f9f28aa6a3f0d719) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_Slingshot02.prefab using Guid(55e8ea4681c4e604f9f28aa6a3f0d719) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e9379b7a78f103c092797a0573f1fdd5') in 0.014077 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectileBolt_Red.prefab
  artifactKey: Guid(0dff80df135423345a0cee68a909bc94) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectileBolt_Red.prefab using Guid(0dff80df135423345a0cee68a909bc94) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '66ba642ae26a1b95e9ee600bbfec5f2c') in 0.016243 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_ZKR.prefab
  artifactKey: Guid(2cd6b7e51b1926d4792057a260a40c2b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_ZKR.prefab using Guid(2cd6b7e51b1926d4792057a260a40c2b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b3d48ab9c4983bc8fdd36bfa5f853e29') in 0.027797 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectileBolt_Yellow.prefab
  artifactKey: Guid(7724746b571749b45963bf46e66e020c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectileBolt_Yellow.prefab using Guid(7724746b571749b45963bf46e66e020c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a661d5cebb26f2e479d6e85079c3fd23') in 0.014663 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectileMinigun.prefab
  artifactKey: Guid(42e9c61b019fdbe46a3317fa0b6b941b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectileMinigun.prefab using Guid(42e9c61b019fdbe46a3317fa0b6b941b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ef26d11c69691dd1620529cf54fae01b') in 0.013739 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectileNormal.prefab
  artifactKey: Guid(df6e092f2ef2b8846ab9430f5fa5a224) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectileNormal.prefab using Guid(df6e092f2ef2b8846ab9430f5fa5a224) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '405a0521a8d8ae5686b477fa38c35deb') in 0.012453 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_TaserGun01.prefab
  artifactKey: Guid(f4329c7337091bb4f8b9bfbf59e0da2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_TaserGun01.prefab using Guid(f4329c7337091bb4f8b9bfbf59e0da2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e211e4e7467afa2d5162dcadcb3c5a36') in 0.054025 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectileBolt_bone03.prefab
  artifactKey: Guid(1ab398c820bb22e498230d78346dec51) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectileBolt_bone03.prefab using Guid(1ab398c820bb22e498230d78346dec51) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8a53afd49695a9981e827770000a5f17') in 0.025238 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectileFlame.prefab
  artifactKey: Guid(df1a48565a868ca4fb3e0250a2aae35b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectileFlame.prefab using Guid(df1a48565a868ca4fb3e0250a2aae35b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'bb41d41aec5a148d085524aa295f5409') in 0.021371 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectileBolt_bone02.prefab
  artifactKey: Guid(f296f8a4d8846454d9e61c397bb4c842) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectileBolt_bone02.prefab using Guid(f296f8a4d8846454d9e61c397bb4c842) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '386d59a75ccba04aa48d76e73c9b5dfb') in 0.019441 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectileMedical.prefab
  artifactKey: Guid(019f863a37ed57e44a0653e6ea406e22) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectileMedical.prefab using Guid(019f863a37ed57e44a0653e6ea406e22) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '22a104273ecc9f49b9145853f86ae873') in 0.014514 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectileBolt_bone01.prefab
  artifactKey: Guid(35cd86c39e08b7144823b2bf1018de58) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectileBolt_bone01.prefab using Guid(35cd86c39e08b7144823b2bf1018de58) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e7b1b5e22241668958a12fa30be30adf') in 0.022642 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectileBolt_Calculator03.prefab
  artifactKey: Guid(dcf787e75ce852e4b85036310258d098) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectileBolt_Calculator03.prefab using Guid(dcf787e75ce852e4b85036310258d098) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6f132f7831248f42ede5429add48c9c7') in 0.024272 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectileLaser.prefab
  artifactKey: Guid(4d479405ee969064fae65325d5cf681b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectileLaser.prefab using Guid(4d479405ee969064fae65325d5cf681b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '85d5bfaf68fd3bd86d84460955ed8e0a') in 0.010000 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_TaserGun02.prefab
  artifactKey: Guid(44a3893a5c57ac84994a20c069e626b9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_TaserGun02.prefab using Guid(44a3893a5c57ac84994a20c069e626b9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a50f0ccd69ffce452b062578355bad62') in 0.026894 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectileBolt_BoneStaff01.prefab
  artifactKey: Guid(4e75a2bccaa852942a9f837a849ddba1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectileBolt_BoneStaff01.prefab using Guid(4e75a2bccaa852942a9f837a849ddba1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd298b1965fa5ab903e628707f51114e7') in 0.051404 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectileNuclear.prefab
  artifactKey: Guid(c858097103cc0bf49803bafd5e4210bb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectileNuclear.prefab using Guid(c858097103cc0bf49803bafd5e4210bb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fbbb35253ccd2fa833c8165c099d0b51') in 0.014053 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectileBolt.prefab
  artifactKey: Guid(55401b9a11382d04cb66162a18b77717) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectileBolt.prefab using Guid(55401b9a11382d04cb66162a18b77717) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4c47f627dcb41a59b5a489f320400963') in 0.014076 seconds 
========================================================================
Received Import Request.
  Time since last request: 7.101015 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectileNuclear1.prefab
  artifactKey: Guid(37e8bbd11ddafb94e88a3096749a4cfe) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectileNuclear1.prefab using Guid(37e8bbd11ddafb94e88a3096749a4cfe) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '050db80bd9d4608307c894e3e7d2e1ae') in 0.016257 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectileShuriken.prefab
  artifactKey: Guid(58bf861f24d69ae48819dc17fda2f509) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectileShuriken.prefab using Guid(58bf861f24d69ae48819dc17fda2f509) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd0c6784c376404e2d5e19f6dd677d2eb') in 0.011999 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectileShredder.prefab
  artifactKey: Guid(e037446165b7a5646b1e290cf9d662bc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectileShredder.prefab using Guid(e037446165b7a5646b1e290cf9d662bc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3b32cea3f9134523098fb2de67f97b3a') in 0.019318 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet _Kingblade01.prefab
  artifactKey: Guid(b2193248fd4106e459d0180b68165542) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet _Kingblade01.prefab using Guid(b2193248fd4106e459d0180b68165542) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'decfde4ed4b418508362fbb2beaf6ce8') in 0.018410 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Resources/Prefabs/Projectiles/EnemyProjectile 1.prefab
  artifactKey: Guid(f2fa3420dc1fec140b392f6fcc68999c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/EnemyProjectile 1.prefab using Guid(f2fa3420dc1fec140b392f6fcc68999c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '979a553e311b47bf234ccb02ae249dd9') in 0.014578 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectileTaser.prefab
  artifactKey: Guid(5d61fb2f4b7384d4681c5151c3bde818) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectileTaser.prefab using Guid(5d61fb2f4b7384d4681c5151c3bde818) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '54ee498ecce9c18e3170a87a117ca62e') in 0.015839 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectileNuclear4.prefab
  artifactKey: Guid(3e56cc774743bca4487cabd3805ba722) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectileNuclear4.prefab using Guid(3e56cc774743bca4487cabd3805ba722) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2319ef701f346ff4182372e3657f02e6') in 0.013210 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet _Blade12.prefab
  artifactKey: Guid(d8078d581e3b05b449bb4a22a1175e2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet _Blade12.prefab using Guid(d8078d581e3b05b449bb4a22a1175e2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1412022215a556ab2eb0558d5ece4927') in 0.032492 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectileObliterator.prefab
  artifactKey: Guid(08beb8d46f1728a48a10624f98bcdab7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectileObliterator.prefab using Guid(08beb8d46f1728a48a10624f98bcdab7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '469a195b77b9e01e3f00cb4c4e6262f8') in 0.016027 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Resources/Prefabs/Projectiles/PillarProjectile.prefab
  artifactKey: Guid(2825fef607019b946bf4ca26cc15de29) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/PillarProjectile.prefab using Guid(2825fef607019b946bf4ca26cc15de29) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8f9d26c98894c88a58a1e0cc8d924606') in 0.013929 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet_SoulScythe01.prefab
  artifactKey: Guid(05afab6e1d0387d4790a1531543cc1dc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet_SoulScythe01.prefab using Guid(05afab6e1d0387d4790a1531543cc1dc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '96f540d64f2696f553d1d4ca38263ec2') in 0.009711 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Resources/Prefabs/Projectiles/EnemyRotatingProjectile.prefab
  artifactKey: Guid(1152a8cb23451b242a5869b0cd81ad44) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/EnemyRotatingProjectile.prefab using Guid(1152a8cb23451b242a5869b0cd81ad44) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '48f4beeae3968f7af6112437aaf9a35f') in 0.017431 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Resources/Prefabs/Projectiles/Effect_bullet_Shieldbow02.prefab
  artifactKey: Guid(d74cd4d613a9b8746b3b7f5188856bcb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/Effect_bullet_Shieldbow02.prefab using Guid(d74cd4d613a9b8746b3b7f5188856bcb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'cd15df84dd4d5382fb01a83fd756483e') in 0.018525 seconds 
========================================================================
Received Import Request.
  Time since last request: 11449.696318 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletLaser.prefab
  artifactKey: Guid(103c3fe48eda9034193febed09ee92a9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletLaser.prefab using Guid(103c3fe48eda9034193febed09ee92a9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5225d1cbe2ed45efab84c575547ecdc0') in 0.015571 seconds 
========================================================================
Received Import Request.
  Time since last request: 111.454541 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile.prefab
  artifactKey: Guid(b978530e91ccb1449916c3610ee9cc58) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile.prefab using Guid(b978530e91ccb1449916c3610ee9cc58) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '05718f747998639c931462cb41889786') in 0.009496 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.575897 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_02_1.prefab
  artifactKey: Guid(afe67a30dda54c84bb5bb203f48ae0ad) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_02_1.prefab using Guid(afe67a30dda54c84bb5bb203f48ae0ad) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6276b2444fae7736f3208955e92d97d7') in 0.009668 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.550456 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_02_3.prefab
  artifactKey: Guid(79161678e29e07047a7dd5de86f28e3b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_02_3.prefab using Guid(79161678e29e07047a7dd5de86f28e3b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '03d33c961f003e7c728f0908aef1ab33') in 0.010935 seconds 
========================================================================
Received Import Request.
  Time since last request: 3.645802 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_02_2.prefab
  artifactKey: Guid(1e8c4293a81eca246a1f4c2e06da3267) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_02_2.prefab using Guid(1e8c4293a81eca246a1f4c2e06da3267) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0fe42d5298c1489cc32b34c658ba5163') in 0.019840 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.850070 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_02_4.prefab
  artifactKey: Guid(1c41bbe46e2599140944f624981d8197) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_02_4.prefab using Guid(1c41bbe46e2599140944f624981d8197) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0499695100400a04a626e2043d945f33') in 0.011523 seconds 
========================================================================
Received Import Request.
  Time since last request: 2.500753 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_04_2.prefab
  artifactKey: Guid(fee4110a843c0ae4481c023419b9a043) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_04_2.prefab using Guid(fee4110a843c0ae4481c023419b9a043) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8fc55d8d75fa2f85894911acf2541e1a') in 0.014460 seconds 
========================================================================
Received Import Request.
  Time since last request: 1.240672 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_04_3.prefab
  artifactKey: Guid(7f6911ec96f251b4dafc7f83ae1579a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_04_3.prefab using Guid(7f6911ec96f251b4dafc7f83ae1579a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'cf77ab3010328457aebe102f019b0495') in 0.017672 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.566030 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_04_4.prefab
  artifactKey: Guid(c9e21a20747c4f84cacf0f667485db0c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_04_4.prefab using Guid(c9e21a20747c4f84cacf0f667485db0c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8b6378762976153e2c30c2e50c6409b5') in 0.012801 seconds 
========================================================================
Received Import Request.
  Time since last request: 1.906552 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_06_1.prefab
  artifactKey: Guid(d8c3fd3d4cdb24e42b121130d163dc3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_06_1.prefab using Guid(d8c3fd3d4cdb24e42b121130d163dc3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '055f043477824be7ccdfc91c14553ab0') in 0.013056 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.463544 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_06_2.prefab
  artifactKey: Guid(019b0a52f3c716c4a9ef8aa5f39384b9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_06_2.prefab using Guid(019b0a52f3c716c4a9ef8aa5f39384b9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'bb923c75fc42a46494b5aadfab3553a0') in 0.018036 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.389985 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_06_3.prefab
  artifactKey: Guid(f65edfb5ca88e09468736065d47804da) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_06_3.prefab using Guid(f65edfb5ca88e09468736065d47804da) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a89d821cbfc0570293efc2b2094308b6') in 0.010015 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.336212 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_06_4.prefab
  artifactKey: Guid(a229b38bc8d51b64bbcff4fc8a72724a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_06_4.prefab using Guid(a229b38bc8d51b64bbcff4fc8a72724a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'da0c5b0c84d13d693f0be77710eed27d') in 0.009589 seconds 
========================================================================
Received Import Request.
  Time since last request: 4.556516 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_21_1.prefab
  artifactKey: Guid(b27931671041e474bb0339ec6fbd460e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_21_1.prefab using Guid(b27931671041e474bb0339ec6fbd460e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '66e7060205490a197a8f14ad60f73f87') in 0.014595 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.451882 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_21_2.prefab
  artifactKey: Guid(55f1aa45779823247a2229291cab75ba) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_21_2.prefab using Guid(55f1aa45779823247a2229291cab75ba) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f0986f51bf3b1737397bc41e7379bb6f') in 0.014089 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.371097 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_21_3.prefab
  artifactKey: Guid(7b3742ba60c122f40889ab4b7ef00ec3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_21_3.prefab using Guid(7b3742ba60c122f40889ab4b7ef00ec3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c129d029293ab8432eac90b85e4414b5') in 0.014257 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.381330 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_21_4.prefab
  artifactKey: Guid(7b408ab801a9a904ba51c9ace9ed8224) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_21_4.prefab using Guid(7b408ab801a9a904ba51c9ace9ed8224) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4a4431ca4b804ec72d55875e1d31c9e8') in 0.014211 seconds 
========================================================================
Received Import Request.
  Time since last request: 2.578718 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_25_1.prefab
  artifactKey: Guid(c53d3f90ca52dba4189628ea6a1d3ef5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_25_1.prefab using Guid(c53d3f90ca52dba4189628ea6a1d3ef5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6a7a51f2250962e159301baa324fde2c') in 0.010049 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.312515 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_25_2.prefab
  artifactKey: Guid(d1a08e929a2c92a46a0d86330f8cd261) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_25_2.prefab using Guid(d1a08e929a2c92a46a0d86330f8cd261) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c598af37b736617b4bed603e3e1d1142') in 0.010385 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.314252 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_25_3.prefab
  artifactKey: Guid(385a1fd2216b60645843adf070620147) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_25_3.prefab using Guid(385a1fd2216b60645843adf070620147) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd899b2781e509c286cac5197d7bfa367') in 0.009651 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.363906 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_25_4.prefab
  artifactKey: Guid(e7f6651ff2b952c4e81f4b68ce87fae3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_25_4.prefab using Guid(e7f6651ff2b952c4e81f4b68ce87fae3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c8b29cb8a5261cf4ecc6937a05603ee4') in 0.010081 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.316370 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_26_1.prefab
  artifactKey: Guid(fa739e1a05e784e45931bf4eb5794790) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_26_1.prefab using Guid(fa739e1a05e784e45931bf4eb5794790) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '673ebfab47d3dd43f4666726497866f5') in 0.015470 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.375038 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_26_2.prefab
  artifactKey: Guid(478fe9507004f7542992b64d96b8b1f7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_26_2.prefab using Guid(478fe9507004f7542992b64d96b8b1f7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c5a00f01ffd23fa5479f567e2447e107') in 0.013935 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.311214 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_26_3.prefab
  artifactKey: Guid(5e3acb34d6bc5f146a455cc4bbae3e60) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_26_3.prefab using Guid(5e3acb34d6bc5f146a455cc4bbae3e60) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '078fef1516ce9ea5bf1f912db271bc09') in 0.016786 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.267524 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_26_4.prefab
  artifactKey: Guid(4ddb6993cfb0acd4482320e081b0a97a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_26_4.prefab using Guid(4ddb6993cfb0acd4482320e081b0a97a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '03ad426c5ed718ee12806a7d4fdacdcc') in 0.011318 seconds 
========================================================================
Received Import Request.
  Time since last request: 4.176102 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_CiLi.prefab
  artifactKey: Guid(a4782ee50147a7647ab6f1225511870a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_CiLi.prefab using Guid(a4782ee50147a7647ab6f1225511870a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5e292e6bc0df610942322ec70488cf2c') in 0.038274 seconds 
========================================================================
Received Import Request.
  Time since last request: 1.368821 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_CombatDrone.prefab
  artifactKey: Guid(fbdbba24b188ae647b4ea8a2b1f09503) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_CombatDrone.prefab using Guid(fbdbba24b188ae647b4ea8a2b1f09503) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c2b09a3f22a225c23d55cf85b3d28fb5') in 0.026116 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.378219 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_Dagger01.prefab
  artifactKey: Guid(4b6f90b99cb6c0b49ad844e69d312ccc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_Dagger01.prefab using Guid(4b6f90b99cb6c0b49ad844e69d312ccc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a234ee52e5b81e241dbed41e166da42a') in 0.022005 seconds 
========================================================================
Received Import Request.
  Time since last request: 2.836852 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_Dagger02.prefab
  artifactKey: Guid(1026f3f54670e254a876404110fd7bca) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_Dagger02.prefab using Guid(1026f3f54670e254a876404110fd7bca) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fd1b7a719a8568e625d43d4ec399ddf5') in 0.019459 seconds 
========================================================================
Received Import Request.
  Time since last request: 1.062174 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_Fireball01.prefab
  artifactKey: Guid(581d9d6e0828f534b8e2d441fd1cecae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_Fireball01.prefab using Guid(581d9d6e0828f534b8e2d441fd1cecae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ba439f552ad41af21d1d15d6bcbe0851') in 0.021682 seconds 
========================================================================
Received Import Request.
  Time since last request: 1.424118 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_Fireball02.prefab
  artifactKey: Guid(582a1138cfe02664182ce64b82f303e7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_Fireball02.prefab using Guid(582a1138cfe02664182ce64b82f303e7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '86487ecffe79049fbcf53660fe3de404') in 0.020798 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.480912 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_Fireball03.prefab
  artifactKey: Guid(d657cec5d0547144994da3afce617fb9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_Fireball03.prefab using Guid(d657cec5d0547144994da3afce617fb9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c6a6a0f29c1255d3fc6ad522ba026edc') in 0.024610 seconds 
========================================================================
Received Import Request.
  Time since last request: 3.044923 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_FlySword1.prefab
  artifactKey: Guid(b136cf74482c5264491323ec4c490a8b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_FlySword1.prefab using Guid(b136cf74482c5264491323ec4c490a8b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e6a95b5fd930dabc5abdc4ed3c65c58a') in 0.007505 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.395492 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_FlySword2.prefab
  artifactKey: Guid(6da989a43136d064d9c6acc67ed26a6d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_FlySword2.prefab using Guid(6da989a43136d064d9c6acc67ed26a6d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '68d47630c80ac121b8bc6fc7f68094fe') in 0.008313 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.351455 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_FlySword3.prefab
  artifactKey: Guid(fd97ed4009ecc3a499ebc5db95e85476) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_FlySword3.prefab using Guid(fd97ed4009ecc3a499ebc5db95e85476) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ed3b22ece3084c297cd5e3d611f42a00') in 0.007647 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.314984 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_FlySword4.prefab
  artifactKey: Guid(cc47b5149b741eb4999de66c5924b047) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_FlySword4.prefab using Guid(cc47b5149b741eb4999de66c5924b047) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'db815e3060ace158140a99cad985925c') in 0.008476 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.620630 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_Laser.prefab
  artifactKey: Guid(ad7693f229a2ff04591c7c799a99dca5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_Laser.prefab using Guid(ad7693f229a2ff04591c7c799a99dca5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2a66efd275fdf2cff62d2c3ca3460835') in 0.009169 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.625900 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_LightningBall01.prefab
  artifactKey: Guid(c742e12f3cc12bb47bb67fd5c0576666) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_LightningBall01.prefab using Guid(c742e12f3cc12bb47bb67fd5c0576666) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0e18dde058aa8fca732b922f25e23549') in 0.024726 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.329519 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_LightningBall02.prefab
  artifactKey: Guid(915c768a7b7422e40a86ee533e82337e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_LightningBall02.prefab using Guid(915c768a7b7422e40a86ee533e82337e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c32989659d8ad4c3a1a13fe239a891cf') in 0.032105 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.283118 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectile_LightningBall03.prefab
  artifactKey: Guid(e4e0743030b03f54e95a9adfec9db682) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectile_LightningBall03.prefab using Guid(e4e0743030b03f54e95a9adfec9db682) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0bc9d827ebcc8cea2790cc8d7248b9cd') in 0.025381 seconds 
========================================================================
Received Import Request.
  Time since last request: 16.804629 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectileBolt_BoneBow02.prefab
  artifactKey: Guid(3cb524ab77edca24785d5c166eca0547) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectileBolt_BoneBow02.prefab using Guid(3cb524ab77edca24785d5c166eca0547) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'da47f1fc2f185c68c8d83b370aa6bd4f') in 0.029003 seconds 
========================================================================
Received Import Request.
  Time since last request: 39.947410 seconds.
  path: Assets/Resources/Prefabs/Projectiles/BulletProjectileBolt_Red.prefab
  artifactKey: Guid(0dff80df135423345a0cee68a909bc94) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectiles/BulletProjectileBolt_Red.prefab using Guid(0dff80df135423345a0cee68a909bc94) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c9c89779121982b8cddfa0a4b0c16eaa') in 0.013234 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.011082 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 3.49 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.152 seconds
Domain Reload Profiling:
	ReloadAssembly (1153ms)
		BeginReloadAssembly (140ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (35ms)
		EndReloadAssembly (907ms)
			LoadAssemblies (91ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (212ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (59ms)
			SetupLoadedEditorAssemblies (404ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (15ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (4ms)
				BeforeProcessingInitializeOnLoad (69ms)
				ProcessInitializeOnLoadAttributes (258ms)
				ProcessInitializeOnLoadMethodAttributes (47ms)
				AfterProcessingInitializeOnLoad (10ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (85ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 11.63 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3218 Unused Serialized files (Serialized files now loaded: 0)
Unloading 108 unused Assets / (102.2 KB). Loaded Objects now: 4051.
Memory consumption went from 226.9 MB to 226.8 MB.
Total: 4.522300 ms (FindLiveObjects: 0.548500 ms CreateObjectMapping: 0.121200 ms MarkObjects: 3.709800 ms  DeleteObjects: 0.141500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 615.527671 seconds.
  path: Assets/ScenesPortrait_dungeon/Prefab/DraggableItemFollowerUIPrefab.prefab
  artifactKey: Guid(e0bca8d0f17a0c94ea611de665250c9e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ScenesPortrait_dungeon/Prefab/DraggableItemFollowerUIPrefab.prefab using Guid(e0bca8d0f17a0c94ea611de665250c9e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd0a9d8332857bf13cafa3c8f2b8e1b1f') in 0.072876 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000245 seconds.
  path: Assets/Prefabs/Items.prefab
  artifactKey: Guid(3bfbd543323063b49b82b002cb8cf91d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Items.prefab using Guid(3bfbd543323063b49b82b002cb8cf91d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '35b312c2f76eebb2aab86af5539b0c1a') in 0.149862 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/GameResources/Dungeon/Singletons/ItemService.asset
  artifactKey: Guid(0bce07fb20ed69c4a99a5fb6f9d6397e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameResources/Dungeon/Singletons/ItemService.asset using Guid(0bce07fb20ed69c4a99a5fb6f9d6397e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) SendMessage cannot be called during Awake, CheckConsistency, or OnValidate (Weapon: OnSpriteTilingPropertyChange)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEditor.AssetDatabase:LoadAssetAtPath<GameData> (string)
SignletonsDataBase`1<GameData>:get_Instance () (at Assets/Scripts/Data/Singletons/SignletonsDataBase.cs:24)
Effect:OnValidate () (at Assets/Scripts/Data/Effect/Effect.cs:121)

[Assets/Scripts/Data/Singletons/SignletonsDataBase.cs line 24]

SendMessage cannot be called during Awake, CheckConsistency, or OnValidate (HidghLight: OnSpriteTilingPropertyChange)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEditor.AssetDatabase:LoadAssetAtPath<GameData> (string)
SignletonsDataBase`1<GameData>:get_Instance () (at Assets/Scripts/Data/Singletons/SignletonsDataBase.cs:24)
Effect:OnValidate () (at Assets/Scripts/Data/Effect/Effect.cs:121)

[Assets/Scripts/Data/Singletons/SignletonsDataBase.cs line 24]

SendMessage cannot be called during Awake, CheckConsistency, or OnValidate (HidghLight: OnSpriteTilingPropertyChange)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEditor.AssetDatabase:LoadAssetAtPath<GameData> (string)
SignletonsDataBase`1<GameData>:get_Instance () (at Assets/Scripts/Data/Singletons/SignletonsDataBase.cs:24)
Effect:OnValidate () (at Assets/Scripts/Data/Effect/Effect.cs:121)

[Assets/Scripts/Data/Singletons/SignletonsDataBase.cs line 24]

 -> (artifact id: '116eddce108ebfa53cf8e3bfd9eb89e6') in 1.510179 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Prefabs/Map/PickupItem.prefab
  artifactKey: Guid(4c545b32e7c22eb4aad7ca0d55aee3c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Map/PickupItem.prefab using Guid(4c545b32e7c22eb4aad7ca0d55aee3c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '67f895b2319bd560a23e0db5b0d49b2e') in 0.004920 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016954 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 2.58 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.259 seconds
Domain Reload Profiling:
	ReloadAssembly (1260ms)
		BeginReloadAssembly (158ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (37ms)
		EndReloadAssembly (968ms)
			LoadAssemblies (108ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (247ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (60ms)
			SetupLoadedEditorAssemblies (414ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (16ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (3ms)
				BeforeProcessingInitializeOnLoad (75ms)
				ProcessInitializeOnLoadAttributes (259ms)
				ProcessInitializeOnLoadMethodAttributes (52ms)
				AfterProcessingInitializeOnLoad (9ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (96ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 12.28 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3218 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (100.7 KB). Loaded Objects now: 4066.
Memory consumption went from 229.0 MB to 228.9 MB.
Total: 4.529100 ms (FindLiveObjects: 0.552300 ms CreateObjectMapping: 0.102800 ms MarkObjects: 3.798300 ms  DeleteObjects: 0.074200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
